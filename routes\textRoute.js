
const express = require('express');
const router = express.Router();
const { generateTextGif } = require('../utils/multiFrameGifCreator');
const { cleanup } = require('../utils/cleanup');


router.post('/', async (req, res) => {
  console.log('=== Received request to /api/text-to-gif ===');
  console.log('Request body:', req.body);
  console.log('Environment check - CLIPDROP_API_KEY:', process.env.CLIPDROP_API_KEY ? 'Present' : 'Missing');

  const { prompt } = req.body;
  if (!prompt) {
    console.log('No prompt provided');
    return res.status(400).json({ error: 'Prompt is required' });
  }

  console.log('Processing prompt:', prompt);

  try {
    // Clean up ALL previous files before generating new ones
    await cleanup(true, true, false);
    console.log('Cleaned up all previous files and GIFs');

    console.log('Calling generateTextGif with prompt:', prompt);
    const gifUrl = await generateTextGif(prompt);
    console.log('Generated GIF URL:', gifUrl);

    const response = { gifUrl };
    console.log('Sending response:', response);
    res.status(200).json(response);
  } catch (error) {
    console.error("GIF generation error:", error.message);
    res.status(500).json({ error: 'Failed to generate GIF', details: error.message });
  }
});

module.exports = router;
