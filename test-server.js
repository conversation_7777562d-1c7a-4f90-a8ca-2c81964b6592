// Test server endpoint
const fetch = require('node-fetch');

async function testServerConnection() {
  try {
    console.log('Testing server connection...');

    // First test if server is reachable
    const healthResponse = await fetch('http://localhost:5000/', {
      method: 'GET'
    });

    console.log('Server health check status:', healthResponse.status);

  } catch (error) {
    console.error('Error connecting to server:', error.message);
    return false;
  }
  return true;
}

async function testServerEndpoint() {
  try {
    console.log('Testing server endpoint...');

    // Test connection first
    const connected = await testServerConnection();
    if (!connected) {
      console.error('Cannot connect to server');
      return;
    }

    const response = await fetch('http://localhost:5000/api/text-to-gif', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'a beautiful sunset'
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Server responded with error:', response.status, errorText);
      return;
    }

    const result = await response.json();
    console.log('Server response:', result);
    console.log('✅ Server endpoint is working correctly!');

  } catch (error) {
    console.error('Error testing server endpoint:', error.message);
    console.error('Full error:', error);
  }
}

testServerEndpoint();
