// server.js
require('dotenv').config();
const express = require('express');
const path = require('path');
const cors = require('cors');

const textRoute = require('./routes/textRoute');
const imageRoute = require('./routes/imageRoute');
const videoRoute = require('./routes/videoRoute');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public'))); // for serving GIFs

// Create necessary directories
const fs = require('fs');
fs.mkdirSync(path.join(__dirname, 'temp/images'), { recursive: true });
fs.mkdirSync(path.join(__dirname, 'temp/videos'), { recursive: true });
fs.mkdirSync(path.join(__dirname, 'public/generated'), { recursive: true });

// Import cleanup utility
const { cleanup } = require('./utils/cleanup');

// Debug route
app.get('/debug', (req, res) => {
    console.log('Debug route hit');
    res.json({ message: 'Debug route working', env: process.env.CLIPDROP_API_KEY ? 'API key present' : 'API key missing' });
});

// Routes
app.use('/api/text-to-gif', textRoute);
app.use('/api/image-to-gif', imageRoute);
app.use('/api/video-to-gif', videoRoute);

// Cleanup endpoint
app.post('/api/cleanup', async (req, res) => {
    try {
        // Get parameters from request body or use defaults
        const { cleanTemp = true, cleanGenerated = false, limitGifs = true, maxGifs = 20 } = req.body;

        await cleanup(cleanTemp, cleanGenerated, limitGifs, maxGifs);
        res.status(200).json({ message: 'Cleanup completed successfully' });
    } catch (error) {
        console.error('Cleanup error:', error);
        res.status(500).json({ error: 'Failed to clean up files', details: error.message });
    }
});

// Global error handler
app.use((err, req, res, next) => {
    console.error('Global error handler caught:', err);
    res.status(500).json({ error: 'Server error', details: err.message });
});

// Schedule periodic cleanup (every hour)
const CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour in milliseconds
setInterval(async () => {
    try {
        console.log('Running scheduled cleanup...');
        // Clean temp files and limit to 50 GIFs, but don't delete all GIFs
        await cleanup(true, false, true, 50);
        console.log('Scheduled cleanup completed');
    } catch (error) {
        console.error('Scheduled cleanup error:', error);
    }
}, CLEANUP_INTERVAL);

app.listen(PORT, () => {
    console.log(`🚀 Server running at http://localhost:${PORT}`);

    // Run initial cleanup on server start - clean everything
    cleanup(true, true, false).then(() => {
        console.log('Initial cleanup completed - all temporary files and GIFs removed');
    }).catch(error => {
        console.error('Initial cleanup error:', error);
    });
});
